package com.bxm.customer.service;

import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 增值交付单校验服务
 *
 * 负责处理不同增值事项名称的特殊校验逻辑：
 * 1. "改账"场景：校验客户服务存在性并更新相关信息
 * 2. "补账"场景：补全缺失的账期数据
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class ValueAddedValidationService {

    @Autowired
    private com.bxm.customer.service.ICCustomerServiceService customerServiceService;

    @Autowired
    private com.bxm.customer.service.ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    /**
     * 根据增值事项名称执行相应的校验逻辑
     *
     * @param orderVO 增值交付单VO
     * @param order 增值交付单实体（用于更新字段信息）
     */
    public void validateByItemName(ValueAddedDeliveryOrderVO orderVO, ValueAddedDeliveryOrder order) {
        if (StringUtils.isEmpty(orderVO.getItemName())) {
            return; // 如果没有指定增值事项名称，跳过特殊校验
        }

        String itemName = orderVO.getItemName().trim();
        log.info("Processing validation for itemName: {}", itemName);

        switch (itemName) {
            case "改账":
                validateAndUpdateForAccountCorrection(orderVO, order);
                break;
            case "补账":
                validateAndSupplementAccountPeriods(orderVO);
                break;
            default:
                log.debug("No special validation required for itemName: {}", itemName);
                break;
        }
    }

    /**
     * 改账场景的校验和更新逻辑
     *
     * @param orderVO 增值交付单VO
     * @param order 增值交付单实体
     */
    private void validateAndUpdateForAccountCorrection(ValueAddedDeliveryOrderVO orderVO, ValueAddedDeliveryOrder order) {
        // 校验 customId 是否提供
        if (orderVO.getCustomId() == null) {
            throw new IllegalArgumentException("改账场景下，客户服务ID(customId)不能为空");
        }

        log.info("Validating customer service existence for customId: {}", orderVO.getCustomId());

        // 查询客户服务是否存在（使用 IService 的 getById 方法）
        CCustomerService customerService = customerServiceService.getById(orderVO.getCustomId());
        if (customerService == null || Boolean.TRUE.equals(customerService.getIsDel())) {
            throw new IllegalArgumentException("指定的客户服务不存在或已被删除，customId: " + orderVO.getCustomId());
        }

        log.info("Customer service found: customerName={}, creditCode={}, updating delivery order fields",
                customerService.getCustomerName(), customerService.getCreditCode());

        // 更新交付单中的相关字段信息，使其与客户服务保持一致
        updateDeliveryOrderFromCustomerService(order, customerService);
    }

    /**
     * 补账场景的校验和账期补全逻辑
     *
     * @param orderVO 增值交付单VO
     */
    @Transactional(rollbackFor = Exception.class)
    private void validateAndSupplementAccountPeriods(ValueAddedDeliveryOrderVO orderVO) {
        // 校验账期范围是否提供
        if (orderVO.getAccountingPeriodStart() == null || orderVO.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("补账场景下，账期开始时间和结束时间不能为空");
        }

        // 校验账期格式和范围
        if (!isValidPeriodFormat(orderVO.getAccountingPeriodStart()) ||
            !isValidPeriodFormat(orderVO.getAccountingPeriodEnd())) {
            throw new IllegalArgumentException("账期格式不正确，应为YYYYMM格式，如：202301");
        }

        // 校验 customerId 是否提供
        if (orderVO.getCustomerId() == null) {
            throw new IllegalArgumentException("补账场景下，客户ID不能为空");
        }

        // 校验客户服务是否存在
        CCustomerService customerService = customerServiceService.getById(orderVO.getCustomerId());
        if (customerService == null || Boolean.TRUE.equals(customerService.getIsDel())) {
            throw new IllegalArgumentException("指定的客户服务不存在或已被删除，customerId: " + orderVO.getCustomerId());
        }

        log.info("Processing account period supplementation for customerId: {}, customerName: {}, period range: {} - {}",
                orderVO.getCustomerId(), customerService.getCustomerName(),
                orderVO.getAccountingPeriodStart(), orderVO.getAccountingPeriodEnd());

        // 获取需要补全的账期列表
        List<Integer> missingPeriods = findMissingPeriods(orderVO.getCustomerId(),
                orderVO.getAccountingPeriodStart(), orderVO.getAccountingPeriodEnd());

        if (!missingPeriods.isEmpty()) {
            log.info("Found {} missing periods to supplement: {}", missingPeriods.size(), missingPeriods);
            createMissingPeriods(orderVO.getCustomerId(), missingPeriods);
        } else {
            log.info("No missing periods found, all periods already exist for customer: {}",
                    customerService.getCustomerName());
        }
    }

    /**
     * 校验账期格式是否正确
     *
     * @param period 账期
     * @return 是否有效
     */
    private boolean isValidPeriodFormat(Integer period) {
        if (period == null) {
            return false;
        }

        int year = period / 100;
        int month = period % 100;

        // 校验年份范围（2000-2099）和月份范围（1-12）
        return year >= 2000 && year <= 2099 && month >= 1 && month <= 12;
    }

    /**
     * 从客户服务数据更新交付单字段
     *
     * @param order 增值交付单实体
     * @param customerService 客户服务实体
     */
    private void updateDeliveryOrderFromCustomerService(ValueAddedDeliveryOrder order, CCustomerService customerService) {
        // 更新客户相关信息
        order.setCustomerId(customerService.getId());
        order.setCustomerName(customerService.getCustomerName());
        order.setCreditCode(customerService.getCreditCode());
        order.setTaxNo(customerService.getTaxNumber());
        order.setTaxpayerType(customerService.getTaxType());

        // 更新部门信息
        order.setBusinessDeptId(customerService.getBusinessDeptId());
        order.setBusinessTopDeptId(customerService.getBusinessTopDeptId());

        // 设置客户服务ID
        order.setCustomId(customerService.getId());

        log.info("Updated delivery order fields from customer service: customerName={}, creditCode={}, taxNo={}",
                customerService.getCustomerName(), customerService.getCreditCode(), customerService.getTaxNumber());
    }

    /**
     * 查找缺失的账期
     *
     * @param customerServiceId 客户服务ID
     * @param startPeriod 开始账期
     * @param endPeriod 结束账期
     * @return 缺失的账期列表
     */
    private List<Integer> findMissingPeriods(Long customerServiceId, Integer startPeriod, Integer endPeriod) {
        List<Integer> missingPeriods = new ArrayList<>();

        // 生成完整的账期范围
        List<Integer> expectedPeriods = generatePeriodRange(startPeriod, endPeriod);

        // 检查每个账期是否存在
        for (Integer period : expectedPeriods) {
            CustomerServicePeriodMonth existingPeriod = customerServicePeriodMonthService
                    .selectByCustomerServiceIdAndPeriod(customerServiceId, period);
            if (existingPeriod == null) {
                missingPeriods.add(period);
            }
        }

        return missingPeriods;
    }

    /**
     * 生成账期范围
     *
     * @param startPeriod 开始账期 (格式: YYYYMM)
     * @param endPeriod 结束账期 (格式: YYYYMM)
     * @return 账期列表
     */
    private List<Integer> generatePeriodRange(Integer startPeriod, Integer endPeriod) {
        List<Integer> periods = new ArrayList<>();

        int startYear = startPeriod / 100;
        int startMonth = startPeriod % 100;
        int endYear = endPeriod / 100;
        int endMonth = endPeriod % 100;

        int currentYear = startYear;
        int currentMonth = startMonth;

        while (currentYear < endYear || (currentYear == endYear && currentMonth <= endMonth)) {
            periods.add(currentYear * 100 + currentMonth);

            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        }

        return periods;
    }

    /**
     * 创建缺失的账期记录
     *
     * @param customerServiceId 客户服务ID
     * @param missingPeriods 缺失的账期列表
     */
    private void createMissingPeriods(Long customerServiceId, List<Integer> missingPeriods) {
        // 获取客户服务信息用于创建账期记录
        CCustomerService customerService = customerServiceService.getById(customerServiceId);
        if (customerService == null || Boolean.TRUE.equals(customerService.getIsDel())) {
            throw new IllegalArgumentException("客户服务不存在或已删除，无法创建账期记录，customerServiceId: " + customerServiceId);
        }

        // 批量创建账期记录，提高性能
        List<CustomerServicePeriodMonth> periodMonthsToCreate = new ArrayList<>();
        for (Integer period : missingPeriods) {
            try {
                CustomerServicePeriodMonth periodMonth = createPeriodMonthRecord(customerService, period);
                periodMonthsToCreate.add(periodMonth);
                log.debug("Prepared period record for creation: customerServiceId={}, period={}", customerServiceId, period);
            } catch (Exception e) {
                log.error("Failed to prepare period record: customerServiceId={}, period={}", customerServiceId, period, e);
                throw new RuntimeException("准备账期记录失败: period=" + period, e);
            }
        }

        // 批量保存
        if (!periodMonthsToCreate.isEmpty()) {
            customerServicePeriodMonthService.saveBatch(periodMonthsToCreate);
            log.info("Successfully created {} missing period records for customerServiceId: {}",
                    periodMonthsToCreate.size(), customerServiceId);
        }
    }

    /**
     * 创建账期月度记录
     *
     * @param customerService 客户服务
     * @param period 账期
     * @return 账期月度记录
     */
    private CustomerServicePeriodMonth createPeriodMonthRecord(CCustomerService customerService, Integer period) {
        CustomerServicePeriodMonth periodMonth = new CustomerServicePeriodMonth();

        // 设置基本信息
        periodMonth.setCustomerServiceId(customerService.getId());
        periodMonth.setPeriod(period);
        periodMonth.setCustomerName(customerService.getCustomerName());
        periodMonth.setCreditCode(customerService.getCreditCode());
        periodMonth.setTaxNumber(customerService.getTaxNumber());
        periodMonth.setTaxType(customerService.getTaxType());
        periodMonth.setServiceNumber(customerService.getServiceNumber());
        periodMonth.setServiceType(2); // 补账类型

        // 设置部门信息
        periodMonth.setBusinessDeptId(customerService.getBusinessDeptId());
        periodMonth.setBusinessTopDeptId(customerService.getBusinessTopDeptId());
        periodMonth.setAdvisorDeptId(customerService.getAdvisorDeptId());
        periodMonth.setAdvisorTopDeptId(customerService.getAdvisorTopDeptId());
        periodMonth.setAccountingDeptId(customerService.getAccountingDeptId());
        periodMonth.setAccountingTopDeptId(customerService.getAccountingTopDeptId());

        // 设置状态信息
        periodMonth.setServiceStatus(customerService.getServiceStatus());
        periodMonth.setYear(period / 100); // 从账期中提取年份

        // 设置创建来源信息
        periodMonth.setAddFromType(2); // 标记为从补账创建

        // 设置默认的交付状态
        periodMonth.setMedicalDeliverStatus(-2); // 无需交付
        periodMonth.setSocialDeliverStatus(-2); // 无需交付
        periodMonth.setPersonTaxDeliverStatus(-2); // 个税-工资薪金无需交付
        periodMonth.setOperationTaxDeliverStatus(-2); // 个税-经营所得无需交付
        periodMonth.setNationalTaxDeliverStatus(-2); // 无需交付
        periodMonth.setPreAuthDeliverStatus(-2); // 无需交付

        return periodMonth;
    }
}
